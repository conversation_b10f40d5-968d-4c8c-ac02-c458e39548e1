"use client"

import { <PERSON>, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Edit, Pause } from "lucide-react"
import { memo, Suspense } from "react"
import { ChartSkeleton } from "@/lib/chart-provider"

const campaignData = {
  id: 1,
  name: "Summer Sale 2023",
  status: "Active",
  objective: "Sales",
  platforms: ["Facebook", "Instagram"],
  budget: "$5,000",
  spent: "$2,345",
  startDate: "2023-06-01",
  endDate: "2023-06-30",
  clicks: 12500,
  conversions: 380,
  roas: 3.2,
  description: "Promotional campaign for summer products with special discounts and offers.",
  targetAudience: "Ages 25-45, interests in fashion and outdoor activities",
}

const performanceData = [
  {
    date: "Jun 01",
    clicks: 320,
    conversions: 10,
    spend: 150,
  },
  {
    date: "Jun 02",
    clicks: 380,
    conversions: 12,
    spend: 160,
  },
  {
    date: "Jun 03",
    clicks: 420,
    conversions: 15,
    spend: 170,
  },
  {
    date: "Jun 04",
    clicks: 450,
    conversions: 18,
    spend: 180,
  },
  {
    date: "Jun 05",
    clicks: 480,
    conversions: 20,
    spend: 190,
  },
  {
    date: "Jun 06",
    clicks: 520,
    conversions: 22,
    spend: 200,
  },
  {
    date: "Jun 07",
    clicks: 550,
    conversions: 25,
    spend: 210,
  },
]

// Simple chart component with performance metrics
const CampaignPerformanceChart = memo(function CampaignPerformanceChart({ data }: { data: any[] }) {
  const totalClicks = data.reduce((sum, item) => sum + item.clicks, 0)
  const totalConversions = data.reduce((sum, item) => sum + item.conversions, 0)
  const totalSpend = data.reduce((sum, item) => sum + item.spend, 0)

  return (
    <div className="h-[250px] w-full space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <div className="text-center p-4 bg-muted rounded">
          <div className="text-2xl font-bold text-blue-600">{totalClicks.toLocaleString()}</div>
          <div className="text-sm text-muted-foreground">Total Clicks</div>
        </div>
        <div className="text-center p-4 bg-muted rounded">
          <div className="text-2xl font-bold text-green-600">{totalConversions}</div>
          <div className="text-sm text-muted-foreground">Total Conversions</div>
        </div>
        <div className="text-center p-4 bg-muted rounded">
          <div className="text-2xl font-bold text-orange-600">${totalSpend}</div>
          <div className="text-sm text-muted-foreground">Total Spend</div>
        </div>
      </div>
      <div className="text-center text-sm text-muted-foreground">
        Performance data from {data.length} days
      </div>
    </div>
  )
})

// Memoized campaign header component for better performance
const CampaignHeader = memo(function CampaignHeader({ campaign }: { campaign: typeof campaignData }) {
  return (
    <div className="mb-6 space-y-4">
      <div>
        <h3 className="text-lg font-semibold">{campaign.name}</h3>
        <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
          {campaign.status}
        </Badge>
      </div>
    </div>
  )
})

export const CampaignDetails = memo(function CampaignDetails() {
  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <CardTitle>Campaign Details</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Pause className="mr-2 h-4 w-4" />
                Pause
              </Button>
              <Button variant="outline" size="sm">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </div>
          <CardDescription>View and manage campaign performance</CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <CampaignHeader campaign={campaignData} />
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div>
            <p className="text-sm text-muted-foreground">Objective</p>
            <p className="font-medium">{campaignData.objective}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Platforms</p>
            <div className="flex flex-wrap gap-1">
              {campaignData.platforms.map((platform) => (
                <Badge key={platform} variant="secondary">
                  {platform}
                </Badge>
              ))}
            </div>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Budget</p>
            <p className="font-medium">{campaignData.budget}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Spent</p>
            <p className="font-medium">{campaignData.spent}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Duration</p>
            <p className="font-medium">
              {campaignData.startDate} to {campaignData.endDate}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">ROAS</p>
            <p className="font-medium">{campaignData.roas}x</p>
          </div>
        </div>

        <Tabs defaultValue="performance">
          <TabsList className="mb-4">
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
          </TabsList>
          <TabsContent value="performance">
            <Suspense fallback={<ChartSkeleton height={250} />}>
              <CampaignPerformanceChart data={performanceData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="details">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">Description</h4>
                <p className="text-sm text-muted-foreground">{campaignData.description}</p>
              </div>
              <div>
                <h4 className="font-medium">Target Audience</h4>
                <p className="text-sm text-muted-foreground">{campaignData.targetAudience}</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
